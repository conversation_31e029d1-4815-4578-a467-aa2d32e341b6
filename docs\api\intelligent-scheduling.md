# 智能排程系统 API 文档

## 概述

智能排程系统提供了完整的API接口，支持智能排程计算、产能数据管理、性能监控等功能。

## 基础信息

- **Base URL**: `http://localhost:5050/api`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 认证

所有API请求都需要在请求头中包含认证令牌：

```http
Authorization: Bearer <your-token>
Content-Type: application/json
```

## 智能排程 API

### 1. 创建排程方案

**POST** `/scheduling/plans`

创建智能排程方案，系统会自动生成多个优化方案供选择。

#### 请求参数

```json
{
  "id": "ORD20241201001",
  "productId": "PROD001",
  "quantity": 1000,
  "requiredDate": "2024-12-31",
  "priority": "normal",
  "customerName": "客户A",
  "options": {
    "constraints": {
      "maxDeliveryDate": "2024-12-31",
      "priorityLevel": "normal",
      "specialRequirements": []
    }
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "orderId": "ORD20241201001",
    "plans": [
      {
        "id": "plan_1701234567890_abc123",
        "type": "earliest_completion",
        "name": "最早完成方案",
        "description": "优先考虑交期，选择最高产能设备组合",
        "schedule": {
          "startDate": "2024-12-01",
          "endDate": "2024-12-11",
          "timeRequirements": {
            "timeInDays": 10
          }
        },
        "resources": {
          "equipment": [...],
          "operators": [...]
        },
        "metrics": {
          "efficiency": 0.85,
          "cost": 8500,
          "resourceUtilization": 0.92
        },
        "deliveryPrediction": {
          "deliveryDate": "2024-12-11",
          "onTimeProb": 0.95,
          "riskLevel": "low"
        }
      }
    ],
    "metadata": {
      "generatedAt": "2024-12-01T10:00:00.000Z",
      "algorithm": "IntelligentScheduler",
      "version": "1.0.0"
    }
  }
}
```

### 2. 获取排程方案

**GET** `/scheduling/plans/{orderId}`

获取指定订单的排程方案。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "orderId": "ORD20241201001",
    "plans": [...],
    "selectedPlanId": "plan_1701234567890_abc123",
    "selectedAt": "2024-12-01T10:30:00.000Z"
  }
}
```

### 3. 选择排程方案

**POST** `/scheduling/plans/{orderId}/{planId}/select`

选择并确认指定的排程方案。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "id": "plan_1701234567890_abc123",
    "name": "最早完成方案",
    "confirmed": true
  },
  "message": "排程方案确认成功"
}
```

### 4. 获取排程统计

**GET** `/scheduling/statistics`

获取排程系统的统计信息。

#### 查询参数

- `startDate` (可选): 开始日期
- `endDate` (可选): 结束日期
- `status` (可选): 状态筛选

#### 响应示例

```json
{
  "success": true,
  "data": {
    "totalOrders": 150,
    "scheduledOrders": 120,
    "completedOrders": 80,
    "onTimeDeliveries": 72,
    "averageLeadTime": 12.5,
    "resourceUtilization": {
      "equipment": 0.85,
      "operators": 0.78
    },
    "planTypes": {
      "earliest_completion": 45,
      "high_efficiency": 35,
      "load_balanced": 25,
      "cost_optimized": 10,
      "low_risk": 5
    }
  }
}
```

## 产能数据 API

### 1. 获取设备产能配置

**GET** `/capacity-data/equipment/{equipmentId}`

获取指定设备的产能配置信息。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "id": "EQ001",
    "name": "设备A",
    "type": "general",
    "capacityPerHour": 100,
    "efficiencyFactor": 1.0,
    "status": "active",
    "operators": [
      {
        "operatorId": "OP001",
        "skillLevel": 3,
        "efficiencyFactor": 1.1
      }
    ]
  }
}
```

### 2. 更新设备产能配置

**PUT** `/capacity-data/equipment/{equipmentId}`

更新设备的产能配置。

#### 请求参数

```json
{
  "capacityPerHour": 120,
  "efficiencyFactor": 1.1,
  "status": "active",
  "maintenanceSchedule": {
    "nextMaintenance": "2024-12-15",
    "maintenanceHours": 4
  }
}
```

### 3. 获取操作员技能信息

**GET** `/capacity-data/operator/{operatorId}/skills`

获取操作员的技能信息。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "operatorId": "OP001",
    "name": "张三",
    "skills": [
      {
        "equipmentId": "EQ001",
        "equipmentType": "general",
        "skillLevel": 3,
        "efficiencyFactor": 1.1,
        "certificationDate": "2024-01-15"
      }
    ],
    "overallEfficiency": 1.05,
    "availability": 0.9
  }
}
```

### 4. 计算产能需求

**POST** `/capacity-data/calculate-requirements`

根据订单信息计算产能需求。

#### 请求参数

```json
{
  "productId": "PROD001",
  "quantity": 1000,
  "requiredDate": "2024-12-31",
  "processFlow": [
    {
      "id": "process1",
      "name": "原料准备",
      "standardTime": 30,
      "setupTime": 10
    }
  ]
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "baseCapacity": {
      "requiredQuantity": 1000,
      "adjustedQuantity": 1020,
      "baseProductionHours": 510
    },
    "equipmentRequirements": {
      "byType": {
        "general": {
          "requiredCount": 2,
          "totalHours": 255,
          "adjustedHours": 268
        }
      }
    },
    "operatorRequirements": {
      "byEquipmentType": {
        "general": {
          "requiredCount": 2,
          "skillLevelRequired": 2
        }
      }
    },
    "timeRequirements": {
      "totalTime": 510,
      "timeInDays": 8.5,
      "estimatedStartDate": "2024-12-20",
      "estimatedEndDate": "2024-12-31"
    }
  }
}
```

## 性能监控 API

### 1. 获取性能统计

**GET** `/performance/stats`

获取系统性能统计信息。

#### 查询参数

- `operation` (可选): 指定操作名称

#### 响应示例

```json
{
  "success": true,
  "data": {
    "global": {
      "totalRequests": 1250,
      "successfulRequests": 1200,
      "failedRequests": 50,
      "averageResponseTime": 1250.5,
      "maxResponseTime": 5000,
      "minResponseTime": 200
    },
    "operations": {
      "generateSchedulePlans": {
        "count": 150,
        "successCount": 145,
        "failureCount": 5,
        "averageTime": 2500.8,
        "maxTime": 8000,
        "minTime": 800
      }
    }
  }
}
```

### 2. 获取缓存统计

**GET** `/performance/cache`

获取缓存系统的统计信息。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "hits": 850,
    "misses": 150,
    "sets": 200,
    "size": 180,
    "hitRate": 0.85,
    "memoryUsage": 52428800
  }
}
```

### 3. 系统健康检查

**GET** `/performance/health`

检查系统健康状态。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2024-12-01T10:00:00.000Z",
    "uptime": 86400,
    "memory": {
      "rss": 134217728,
      "heapTotal": 67108864,
      "heapUsed": 45088768,
      "external": 2097152
    },
    "performance": {
      "global": {
        "totalRequests": 1250,
        "averageResponseTime": 1250.5
      }
    }
  }
}
```

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "code": "ERROR_CODE"
}
```

### 常见错误码

| HTTP状态码 | 错误码 | 描述 |
|-----------|--------|------|
| 400 | BAD_REQUEST | 请求参数错误 |
| 401 | UNAUTHORIZED | 未授权访问 |
| 403 | FORBIDDEN | 权限不足 |
| 404 | NOT_FOUND | 资源不存在 |
| 429 | RATE_LIMIT_EXCEEDED | 请求频率超限 |
| 500 | INTERNAL_ERROR | 服务器内部错误 |

## 请求限制

- 每个IP每分钟最多100个请求
- 单个请求最大大小: 10MB
- 请求超时时间: 30秒

## 版本控制

API版本通过URL路径进行控制：

- 当前版本: `v1` (默认)
- 访问方式: `/api/v1/scheduling/plans`

## 示例代码

### JavaScript (Fetch)

```javascript
// 创建排程方案
const response = await fetch('/api/scheduling/plans', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    id: 'ORD20241201001',
    productId: 'PROD001',
    quantity: 1000,
    requiredDate: '2024-12-31',
    priority: 'normal'
  })
});

const result = await response.json();
console.log(result);
```

### cURL

```bash
# 创建排程方案
curl -X POST http://localhost:5050/api/scheduling/plans \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "id": "ORD20241201001",
    "productId": "PROD001",
    "quantity": 1000,
    "requiredDate": "2024-12-31",
    "priority": "normal"
  }'
```

## 更新日志

### v1.0.0 (2024-12-01)
- 初始版本发布
- 支持智能排程计算
- 支持产能数据管理
- 支持性能监控

---

更多详细信息请参考各个模块的具体文档。
