# 设备健康度评估 API 接口文档

## 概述

设备健康度评估系统基于四维度评估体系，提供设备健康度计算、故障预测和维护建议功能。

## 评估体系

### 四维度权重分配
- 设备年龄：20%
- 维修频率：30% 
- 故障严重程度：30%
- 保养情况：20%

### 健康度等级
- 90-100分：优秀
- 80-89分：良好
- 70-79分：一般
- 60-69分：较差
- 0-59分：危险

## API 接口

### 1. 计算设备健康度

**POST** `/api/equipment/:id/health/calculate`

计算指定设备的健康度评估。

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | string | 是 | 设备ID |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "equipmentId": "EQ001",
    "totalScore": 78,
    "level": "一般",
    "assessmentDate": "2024-07-22T10:30:00.000Z",
    "dimensions": {
      "age": {
        "score": 80,
        "weight": 0.2,
        "details": {
          "ageInYears": 3.5,
          "category": "3-5年"
        }
      },
      "repairFrequency": {
        "score": 75,
        "weight": 0.3,
        "details": {
          "annualRepairRate": 2.1,
          "totalRepairs": 7,
          "category": "2-3次/年"
        }
      },
      "faultSeverity": {
        "score": 70,
        "weight": 0.3,
        "details": {
          "weightedDeduction": 30,
          "severeCount": 1,
          "moderateCount": 3,
          "minorCount": 3
        }
      },
      "maintenance": {
        "score": 85,
        "weight": 0.2,
        "details": {
          "maintenanceRatio": 0.6,
          "daysSinceLastMaintenance": 45,
          "ratioScore": 60,
          "recentScore": 90
        }
      }
    },
    "recommendations": [
      {
        "priority": "medium",
        "type": "maintenance_plan",
        "title": "制定定期保养计划",
        "description": "保养不够及时，建议制定并严格执行定期保养计划",
        "estimatedCost": "low",
        "timeframe": "下月开始"
      }
    ]
  },
  "message": "健康度计算成功"
}
```

### 2. 获取设备健康度历史

**GET** `/api/equipment/:id/health/history`

获取设备健康度历史记录。

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | number | 否 | 返回记录数量，默认30 |
| startDate | string | 否 | 开始日期 |
| endDate | string | 否 | 结束日期 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "equipmentId": "EQ001",
    "history": [
      {
        "assessmentDate": "2024-07-22",
        "totalScore": 78,
        "level": "一般",
        "ageScore": 80,
        "repairFrequencyScore": 75,
        "faultSeverityScore": 70,
        "maintenanceScore": 85
      }
    ],
    "trends": {
      "totalScore": {
        "current": 78,
        "previous": 82,
        "change": -4,
        "trend": "decreasing"
      }
    }
  },
  "message": "获取历史记录成功"
}
```

### 3. 故障预测

**GET** `/api/equipment/:id/health/prediction`

基于历史维修记录预测下次故障时间。

#### 响应示例

```json
{
  "success": true,
  "data": {
    "equipmentId": "EQ001",
    "canPredict": true,
    "prediction": {
      "predictedDate": "2024-09-15T00:00:00.000Z",
      "confidence": 70,
      "avgInterval": 120,
      "dataQuality": "good"
    },
    "analysis": {
      "totalRepairs": 5,
      "intervals": [110, 125, 115, 130],
      "avgInterval": 120,
      "standardDeviation": 8.2,
      "coefficientOfVariation": 0.068
    }
  },
  "message": "故障预测成功"
}
```

### 4. 获取健康度统计

**GET** `/api/equipment/health/statistics`

获取所有设备的健康度统计信息。

#### 查询参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| area | string | 否 | 厂区筛选 |
| level | string | 否 | 健康度等级筛选 |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "overview": {
      "totalEquipment": 25,
      "averageHealth": 82.5,
      "lastUpdated": "2024-07-22T10:30:00.000Z"
    },
    "distribution": {
      "excellent": 8,
      "good": 10,
      "average": 5,
      "poor": 2,
      "dangerous": 0
    },
    "trends": {
      "thisMonth": 82.5,
      "lastMonth": 85.2,
      "change": -2.7,
      "trend": "decreasing"
    },
    "warningEquipment": [
      {
        "equipmentId": "EQ003",
        "equipmentName": "生产线A-设备3",
        "totalScore": 65,
        "level": "较差",
        "urgentRecommendations": 2
      }
    ]
  },
  "message": "获取统计信息成功"
}
```

### 5. 批量计算健康度

**POST** `/api/equipment/health/batch-calculate`

批量计算多个设备的健康度。

#### 请求体

```json
{
  "equipmentIds": ["EQ001", "EQ002", "EQ003"],
  "options": {
    "includeRecommendations": true,
    "includePrediction": false
  }
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "results": [
      {
        "equipmentId": "EQ001",
        "totalScore": 78,
        "level": "一般",
        "calculatedAt": "2024-07-22T10:30:00.000Z"
      }
    ],
    "summary": {
      "total": 3,
      "successful": 3,
      "failed": 0,
      "averageScore": 79.3
    }
  },
  "message": "批量计算完成"
}
```

## 错误响应

### 错误格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "code": "ERROR_CODE"
}
```

### 错误代码

| 代码 | 说明 |
|------|------|
| EQUIPMENT_NOT_FOUND | 设备不存在 |
| INSUFFICIENT_DATA | 数据不足，无法计算 |
| CALCULATION_ERROR | 计算过程出错 |
| INVALID_PARAMETERS | 参数无效 |
| PERMISSION_DENIED | 权限不足 |

## 权限要求

所有接口都需要 `equipment_health` 权限。
