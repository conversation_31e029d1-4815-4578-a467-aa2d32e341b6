# 维修保养记录Excel导出格式实现详解

## 📋 概述

本文档详细说明了维修保养记录页面的Excel导出功能实现，包括表格格式、布局、行高、列宽设置的核心代码。该实现基于ExcelJS库，通过精确的单位转换系数确保导出的Excel表格符合指定的厘米尺寸要求。

## 🔧 核心技术栈

- **后端库**: ExcelJS - 用于生成Excel文件
- **文件位置**: `backend/modules/business/export-service.js`
- **主要方法**: `exportMaintenanceToExcelBuffer()`

## 📐 精确单位转换系数

### 1. 基于实际测量的转换系数

```javascript
// 基于实际测量的精确转换系数
// 实测：设置13磅 → 实际0.3cm，所以 13/0.3 = 43.33 (磅/cm)
const MEASURED_POINTS_PER_CM = 43.33;

// 基于实际测量的精确列宽转换系数
// 实测：设置10.4字符 → 实际2.01cm，所以 10.4/2.01 = 5.17 (字符/cm)
const MEASURED_CHARS_PER_CM = 5.17;
```

### 2. 转换系数说明

- **行高转换**: 1厘米 = 43.33磅
- **列宽转换**: 1厘米 = 5.17字符宽度
- **精度控制**: 使用 `Math.round(value * 10) / 10` 确保小数点后一位精度

## 📊 表格布局结构

### 1. 页面设置

```javascript
// 设置页面为A4横印
worksheet.pageSetup = {
    paperSize: 9, // A4
    orientation: 'landscape', // 横印
    fitToPage: true,
    fitToWidth: 1,
    fitToHeight: 0,
    margins: {
        left: 0.7,
        right: 0.7,
        top: 0.75,
        bottom: 0.75,
        header: 0.3,
        footer: 0.3
    }
};
```

### 2. 表格行结构

| 行号 | 内容 | 高度 | 说明 |
|------|------|------|------|
| 第1行 | 版本号"SO4-09406 R:1.0" | 0.46cm | 右上方显示 |
| 第2行 | 主标题"维修及保养记录" | 0.79cm | 居中合并A2:G2 |
| 第3行 | 列标题行 | 0.46cm | 表头样式 |
| 第4行起 | 数据行 | 动态计算 | 根据内容自动调整 |

## 📏 列宽设置详解

### 1. 列宽配置表

```javascript
const headers = [
    { header: '开始时间', key: 'startTime', width: Math.round(3.5 * MEASURED_CHARS_PER_CM * 10) / 10 },        // 3.5cm
    { header: '结束时间', key: 'endTime', width: Math.round(3.5 * MEASURED_CHARS_PER_CM * 10) / 10 },          // 3.5cm
    { header: '机台名称', key: 'deviceName', width: Math.round(6.5 * MEASURED_CHARS_PER_CM * 10) / 10 },       // 6.5cm
    { header: '保养/维修', key: 'type', width: Math.round(1.73 * MEASURED_CHARS_PER_CM * 10) / 10 },           // 1.73cm
    { header: '维修/保养记录', key: 'description', width: Math.round(6.5 * MEASURED_CHARS_PER_CM * 10) / 10 },   // 6.5cm
    { header: '机修人员', key: 'operator', width: Math.round(1.59 * MEASURED_CHARS_PER_CM * 10) / 10 },        // 1.59cm
    { header: '审查', key: 'reviewer', width: Math.round(0.92 * MEASURED_CHARS_PER_CM * 10) / 10 }             // 0.92cm
];
```

### 2. 列宽计算对照表

| 列名 | 厘米要求 | ExcelJS设置 | 计算公式 |
|------|----------|-------------|----------|
| 开始时间 | 3.5cm | 18.1字符 | 3.5 × 5.17 = 18.1 |
| 结束时间 | 3.5cm | 18.1字符 | 3.5 × 5.17 = 18.1 |
| 机台名称 | 6.5cm | 33.6字符 | 6.5 × 5.17 = 33.6 |
| 保养/维修 | 1.73cm | 8.9字符 | 1.73 × 5.17 = 8.9 |
| 维修/保养记录 | 6.5cm | 33.6字符 | 6.5 × 5.17 = 33.6 |
| 机修人员 | 1.59cm | 8.2字符 | 1.59 × 5.17 = 8.2 |
| 审查 | 0.92cm | 4.8字符 | 0.92 × 5.17 = 4.8 |

## 📐 行高动态计算

### 1. 文本行数计算方法

```javascript
// 计算文本在指定列宽下需要的行数
calculateTextLines(text, columnWidthCm) {
    if (!text || text.toString().trim() === '') return 1;

    const textStr = text.toString();
    const MEASURED_CHARS_PER_CM = 5.17; // 基于实际测量的字符/厘米转换系数
    const charsPerLine = Math.floor(columnWidthCm * MEASURED_CHARS_PER_CM);

    // 按换行符分割文本
    const paragraphs = textStr.split(/\r?\n/);
    let totalLines = 0;

    paragraphs.forEach(paragraph => {
        if (paragraph.trim() === '') {
            totalLines += 1; // 空行也占一行
        } else {
            // 计算每个段落需要的行数
            let charCount = 0;
            for (let i = 0; i < paragraph.length; i++) {
                const char = paragraph[i];
                // 中文字符占2个字符宽度，英文字符占1个字符宽度
                const charWidth = /[\u4e00-\u9fff]/.test(char) ? 2 : 1;
                charCount += charWidth;
            }
            const linesNeeded = Math.ceil(charCount / charsPerLine);
            totalLines += Math.max(1, linesNeeded); // 至少1行
        }
    });

    return Math.max(1, totalLines); // 至少返回1行
}
```

### 2. 动态行高计算

```javascript
// 动态计算行高以适应换行内容
const deviceNameLines = this.calculateTextLines(deviceDisplayName, 6.5); // 机台名称列宽6.5cm
const descriptionLines = this.calculateTextLines(record.description || '', 6.5); // 维修/保养记录列宽6.5cm
const maxLines = Math.max(1, deviceNameLines, descriptionLines); // 至少1行

// 基础行高0.46cm，每增加一行增加0.46cm
const calculatedHeight = Math.round(0.46 * maxLines * MEASURED_POINTS_PER_CM * 10) / 10;
row.height = calculatedHeight;
```

## 🎨 样式设置详解

### 1. 版本号样式（第1行）

```javascript
// 在第1行右上方添加版本号"SO4-09406 R:1.0"
const versionCell = worksheet.getCell('G1');
versionCell.value = 'SO4-09406 R:1.0';
versionCell.font = {
    bold: false,
    size: 10,
    name: '宋体'
};
versionCell.alignment = {
    horizontal: 'right',
    vertical: 'top'
};
// 设置第1行高度
worksheet.getRow(1).height = Math.round(0.46 * MEASURED_POINTS_PER_CM * 10) / 10;
```

### 2. 主标题样式（第2行）

```javascript
// 在第2行添加主标题"维修及保养记录"
worksheet.mergeCells('A2:G2');
const titleCell = worksheet.getCell('A2');
titleCell.value = '维修及保养记录';
titleCell.font = {
    bold: true,
    size: 18,
    name: '宋体'
};
titleCell.alignment = {
    horizontal: 'center',
    vertical: 'middle'
};
worksheet.getRow(2).height = Math.round(0.79 * MEASURED_POINTS_PER_CM * 10) / 10;
```

### 3. 列标题样式（第3行）

```javascript
// 在第3行添加列标题
const headerRow = worksheet.getRow(3);
headers.forEach((header, index) => {
    const cell = headerRow.getCell(index + 1);
    cell.value = header.header;
    cell.font = { bold: true, name: '宋体', size: 10 }; // 设置为10号字体
    cell.alignment = {
        horizontal: 'center',
        vertical: 'middle',
        wrapText: false // 列标题不自动换行
    };
    cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F3FF' }
    };
});
// 设置列标题行高为0.46cm
headerRow.height = Math.round(0.46 * MEASURED_POINTS_PER_CM * 10) / 10;
```

### 4. 数据行样式

```javascript
// 设置每个单元格的样式
for (let col = 1; col <= 7; col++) {
    const cell = row.getCell(col);
    cell.font = { name: '宋体', size: 10 }; // 设置为10号字体
    cell.alignment = {
        horizontal: col === 3 || col === 5 ? 'left' : 'center', // 机台名称和维修记录左对齐，其他居中
        vertical: 'middle',
        wrapText: col === 3 || col === 5 // 只有机台名称(第3列)和维修/保养记录(第5列)自动换行
    };
}
```

## 🕐 时间格式化

### 1. 时间格式化方法

```javascript
// 格式化日期时间（不显示秒）- 用于维修保养记录导出
formatDateTimeNoSeconds(dateTimeString) {
    if (!dateTimeString) return '';
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateTimeString;
    }
}
```

### 2. 时间数据填充

```javascript
// 设置单元格值（时间只显示时和分，不显示秒）
row.getCell(1).value = record.startTime ? this.formatDateTimeNoSeconds(record.startTime) : '-';
row.getCell(2).value = record.endTime ? this.formatDateTimeNoSeconds(record.endTime) : '-';
```

## 🖼️ 边框设置

```javascript
// 设置边框（从第3行开始，跳过版本号行和标题行）
for (let rowNum = 3; rowNum <= currentRow - 1; rowNum++) {
    const row = worksheet.getRow(rowNum);
    row.eachCell((cell) => {
        cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
        };
    });
}
```

## 🔄 完整导出流程

### 1. 主要导出方法

```javascript
async exportMaintenanceToExcelBuffer(records, devices = [], filename = null) {
    try {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('维修保养记录');
        
        // 1. 设置页面格式
        // 2. 设置转换系数
        // 3. 添加版本号
        // 4. 添加主标题
        // 5. 设置列宽
        // 6. 添加列标题
        // 7. 添加数据行
        // 8. 设置边框
        // 9. 生成Buffer
        
        const buffer = await workbook.xlsx.writeBuffer();
        return {
            success: true,
            buffer: buffer,
            message: '维修保养记录导出成功'
        };
    } catch (error) {
        console.error('导出维修保养记录失败:', error);
        return {
            success: false,
            message: '导出维修保养记录失败: ' + error.message
        };
    }
}
```

## 🎯 关键特性

1. **精确尺寸控制**: 基于实际测量的转换系数确保厘米级精度
2. **动态行高**: 根据文本内容自动计算行高
3. **中英文字符识别**: 中文字符占2个字符宽度，英文字符占1个字符宽度
4. **自动换行**: 机台名称和维修记录列支持自动换行
5. **版本标识**: 右上方显示版本号"SO4-09406 R:1.0"
6. **时间格式优化**: 时间显示到分钟，不显示秒
7. **A4横印布局**: 适合打印的页面设置

## 📝 使用示例

```javascript
// 调用导出方法
const result = await exportService.exportMaintenanceToExcelBuffer(records, devices, filename);
if (result.success) {
    // 设置响应头并发送文件
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
    res.send(result.buffer);
}
```

这个实现确保了导出的Excel表格完全符合指定的格式要求，包括精确的行高、列宽控制和专业的表格样式。
