<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>已审核 - Makrite管理系统</title>
    <script src="/js/libs/tailwindcss.min.js"></script>
    <link rel="stylesheet" href="/assets/css/application-template.css">
    <link rel="stylesheet" href="/assets/css/common.css">

    <link rel="stylesheet" href="/assets/css/sidebar-scrollbar.css">
</head>
<body class="bg-gray-100">
    <!-- 加载中显示 -->
    <div id="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
    </div>

    <div id="app" class="flex" v-cloak>
        <!-- 侧边导航栏 -->
        <sidebar
            :user="currentUser">
        </sidebar>

        <div class="flex-1 ml-72 p-6">
            <!-- 页面头部 -->
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">已审核申请</h1>
                        <p class="mt-2 text-gray-600">查看和管理所有已完成审核的申请记录</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <!-- 状态指示器 -->
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                <span>已通过</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                <span>已拒绝</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 已审核申请列表 -->
            <approved-application-list
                @view-detail="viewDetail">
            </approved-application-list>
        </div>

        <!-- 详情模态框 -->
        <application-detail
            :application="currentDetail"
            :visible="showDetailModal"
            @close="closeDetail">
        </application-detail>
    </div>

    <script src="/js/libs/vue.global.js"></script>
    <script src="/js/libs/axios.min.js"></script>
    <script type="module" src="/scripts/pages/application/approved.js"></script>
</body>
</html>
