/**
 * 申请书模板组件
 * 用于生成和显示申请书模板，支持PDF下载
 */

export default {
    props: {
        application: Object,
        visible: Boolean
    },
    emits: ['close'],
    setup(props, { emit }) {
        const { ref, computed, onMounted, watch } = Vue;

        // 格式化日期 (YYYY-MM-DD 转为 YYYY年MM月DD日)
        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        }

        // 输入内容安全化
        function sanitizeInput(input) {
            if (!input) return '';
            return input.toString()
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#x27;');
        }

        // 关闭申请书模板
        function closeApplicationTemplate() {
            emit('close');
        }

        // 调整表格高度以填满A4页面
        function adjustTableHeight() {
            // 获取A4页面的高度
            const a4Page = document.getElementById('applicationTemplatePage');
            if (!a4Page) return;
            
            const a4Height = a4Page.clientHeight;

            // 获取表格
            const table = document.querySelector('.application-template');
            if (!table) return;

            // 设置表格高度为A4页面高度的90%
            table.style.height = (a4Height * 0.90) + 'px';

            // 获取内容行和其他行
            const contentRow = table.querySelector('tr:nth-child(3)');
            const otherRows = Array.from(table.querySelectorAll('tr')).filter(row => row !== contentRow);

            // 计算其他行的总高度
            let otherRowsHeight = 0;
            otherRows.forEach(row => {
                otherRowsHeight += row.offsetHeight;
            });

            // 计算内容行应该的高度
            const contentHeight = (a4Height * 0.90) - otherRowsHeight;

            // 设置内容行的高度
            if (contentRow && contentHeight > 100) {
                contentRow.style.height = contentHeight + 'px';
            }
        }

        // 加载PDF相关库
        function loadPdfLibraries() {
            return new Promise((resolve, reject) => {
                // 检查是否已经加载
                if (window.html2canvas && window.jspdf) {
                    resolve();
                    return;
                }

                let loadedCount = 0;
                const totalLibs = 2;

                function checkComplete() {
                    loadedCount++;
                    if (loadedCount === totalLibs) {
                        resolve();
                    }
                }

                // 加载html2canvas
                if (!window.html2canvas) {
                    const script1 = document.createElement('script');
                    script1.src = '/js/libs/html2canvas.min.js';
                    script1.onload = checkComplete;
                    script1.onerror = () => reject(new Error('Failed to load html2canvas'));
                    document.head.appendChild(script1);
                } else {
                    checkComplete();
                }

                // 加载jsPDF
                if (!window.jspdf) {
                    const script2 = document.createElement('script');
                    script2.src = '/js/libs/jspdf.umd.min.js';
                    script2.onload = checkComplete;
                    script2.onerror = () => reject(new Error('Failed to load jsPDF'));
                    document.head.appendChild(script2);
                } else {
                    checkComplete();
                }
            });
        }

        // 下载申请书模板为PDF
        async function downloadApplicationTemplate() {
            const element = document.getElementById('applicationTemplatePage');
            if (!element) {
                alert('申请书模板元素未找到');
                return;
            }

            const filename = '申请书_' + new Date().toISOString().slice(0, 10) + '.pdf';

            // 显示加载提示
            const loadingMsg = document.createElement('div');
            loadingMsg.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
            loadingMsg.textContent = '正在生成PDF，请稍候...';
            document.body.appendChild(loadingMsg);

            try {
                // 先加载PDF库
                await loadPdfLibraries();

                // 使用html2canvas将元素转换为canvas
                const canvas = await html2canvas(element, {
                    scale: 2, // 提高清晰度
                    useCORS: true, // 允许加载跨域图片
                    logging: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                });

                // 使用jsPDF将canvas转换为PDF
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                // 计算宽高比例
                const imgData = canvas.toDataURL('image/png');
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const canvasWidth = canvas.width;
                const canvasHeight = canvas.height;
                const ratio = Math.min(pageWidth / canvasWidth, pageHeight / canvasHeight);
                const imgWidth = canvasWidth * ratio;
                const imgHeight = canvasHeight * ratio;

                // 添加图像到PDF，调整y坐标为-5mm，使内容上移
                pdf.addImage(imgData, 'PNG', 0, -5, imgWidth, imgHeight);

                // 下载PDF
                pdf.save(filename);

                // 移除加载提示
                document.body.removeChild(loadingMsg);
            } catch (error) {
                console.error('生成PDF失败:', error);
                alert('生成PDF失败，请重试');
                if (document.body.contains(loadingMsg)) {
                    document.body.removeChild(loadingMsg);
                }
            }
        }

        // 生成申请书模板HTML
        const templateHTML = computed(() => {
            if (!props.application) return '';

            const application = props.application;
            
            // 格式化日期
            const formattedDate = formatDate(application.date);

            // 获取审批信息
            let factoryApproval = '';
            let directorApproval = '';
            let managerApproval = '';

            // 处理厂长审批信息
            if (application.approvals && application.approvals.directors) {
                const directorsEntries = Object.entries(application.approvals.directors);
                if (directorsEntries.length > 0) {
                    factoryApproval = `<div class="approval-container">`;
                    directorsEntries.forEach(([username, approval]) => {
                        if (approval.status === 'approved' || approval.status === 'rejected') {
                            factoryApproval += `
                                <div class="single-approval">
                                    ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                                    <div class="signature-wrapper">
                                        ${approval.signature ?
                                            `<img src="${approval.signature}" alt="${approval.approverName || username}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                            `<div class="no-signature">无签名</div>`
                                        }
                                        <div class="approval-date">${formatDate(approval.date)}</div>
                                    </div>
                                </div>
                            `;
                        }
                    });
                    factoryApproval += `</div>`;
                }
            }

            // 处理总监审批信息
            if (application.approvals && application.approvals.chief) {
                const chiefApproval = application.approvals.chief;
                if (chiefApproval.status === 'approved' || chiefApproval.status === 'rejected') {
                    directorApproval = `
                        <div class="approval-container">
                            <div class="single-approval">
                                ${chiefApproval.comment ? `<div class="approval-comment">${sanitizeInput(chiefApproval.comment)}</div>` : ''}
                                <div class="signature-wrapper">
                                    ${chiefApproval.signature ?
                                        `<img src="${chiefApproval.signature}" alt="${chiefApproval.approverName || '总监'}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                        `<div class="no-signature">无签名</div>`
                                    }
                                    <div class="approval-date">${formatDate(chiefApproval.date)}</div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }

            // 处理经理审批信息（包含CEO签名）
            if (application.approvals && application.approvals.managers) {
                const managersEntries = Object.entries(application.approvals.managers);
                if (managersEntries.length > 0) {
                    managerApproval = `<div class="approval-container">`;
                    managersEntries.forEach(([username, approval]) => {
                        if (approval.status === 'approved' || approval.status === 'rejected') {
                            managerApproval += `
                                <div class="single-approval">
                                    ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                                    <div class="signature-wrapper">
                                        ${approval.signature ?
                                            `<img src="${approval.signature}" alt="${approval.approverName || username}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                            `<div class="no-signature">无签名</div>`
                                        }
                                        <div class="approval-date">${formatDate(approval.date)}</div>
                                    </div>
                                </div>
                            `;
                        }
                    });

                    // 添加CEO审批信息到经理核准区域
                    if (application.approvals.ceo && (application.approvals.ceo.status === 'approved' || application.approvals.ceo.status === 'rejected')) {
                        const ceoApproval = application.approvals.ceo;
                        const ceoSignature = ceoApproval.signature;

                        managerApproval += `
                            <div class="single-approval">
                                ${ceoApproval.comment ? `<div class="approval-comment">${sanitizeInput(ceoApproval.comment)}</div>` : ''}
                                <div class="signature-wrapper">
                                    ${ceoSignature ?
                                        `<img src="${ceoSignature}" alt="CEO的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                        `<div class="no-signature">无签名</div>`
                                    }
                                    <div class="approval-date">${formatDate(ceoApproval.date)}</div>
                                </div>
                            </div>
                        `;
                    }

                    managerApproval += `</div>`;
                }
            }

            // 检测是否为移动设备
            const isMobile = window.innerWidth <= 768;
            const isSmallMobile = window.innerWidth <= 480;

            // 根据设备类型调整样式
            let mobileClass = '';
            if (isSmallMobile) {
                mobileClass = 'mobile-small';
            } else if (isMobile) {
                mobileClass = 'mobile';
            }

            return `
                <table class="application-template ${mobileClass}">
                    <tr>
                        <th colspan="4" class="title">申 请 书</th>
                    </tr>
                    <tr style="height: 5%;">
                        <td class="label">申请部门</td>
                        <td>${sanitizeInput(application.department)}</td>
                        <td class="label">申请日期</td>
                        <td>${formattedDate}</td>
                    </tr>
                    <tr>
                        <td class="label" rowspan="2">申请事由</td>
                        <td colspan="3" class="content">${sanitizeInput(application.content).replace(/\n/g, '<br>')}</td>
                    </tr>
                    <tr style="height: 5%;">
                        <td colspan="3" class="applicant">申请人: ${sanitizeInput(application.applicant)}</td>
                    </tr>
                    <tr>
                        <td class="label">厂长意见</td>
                        <td colspan="3" class="approval">${factoryApproval}</td>
                    </tr>
                    <tr>
                        <td class="label">总监意见</td>
                        <td colspan="3" class="approval">${directorApproval}</td>
                    </tr>
                    <tr>
                        <td class="label">经理核准</td>
                        <td colspan="3" class="approval">${managerApproval}</td>
                    </tr>
                </table>
            `;
        });

        // 监听模板显示状态，调整表格高度
        watch(() => props.visible, (newVal) => {
            if (newVal) {
                // 延迟执行，确保DOM已渲染
                setTimeout(() => {
                    adjustTableHeight();
                }, 100);
            }
        });

        return {
            templateHTML,
            closeApplicationTemplate,
            downloadApplicationTemplate
        };
    },
    template: `
        <!-- 申请书模板预览模态框 -->
        <div v-if="visible" id="applicationTemplateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-start overflow-y-auto z-50 p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full relative">
                <div class="p-4 border-b">
                    <h2 class="text-xl font-bold">申请书</h2>
                    <button @click="closeApplicationTemplate" class="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4 overflow-auto" style="max-height: 80vh;">
                    <div class="a4-page" id="applicationTemplatePage">
                        <div id="applicationTemplateContent" v-html="templateHTML"></div>
                    </div>
                </div>
                <div class="p-4 border-t flex justify-end space-x-2 template-actions">
                    <button @click="downloadApplicationTemplate" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                        下载
                    </button>
                    <button @click="closeApplicationTemplate" class="bg-gray-300 hover:bg-gray-400 px-4 py-2 rounded">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    `
};
