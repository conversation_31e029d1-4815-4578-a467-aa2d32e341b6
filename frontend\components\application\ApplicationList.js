/**
 * 申请列表组件
 * 显示申请列表和搜索功能
 */

import { PRIORITIES } from '../../scripts/config.js';
import { getApplications, deleteApplication } from '../../scripts/api/application.js';
import UnifiedPagination from '../common/UnifiedPagination.js';
import toast from '../../scripts/utils/toast.js';

export default {
    components: {
        UnifiedPagination
    },
    setup(props, { emit }) {
        const { ref, computed, onMounted } = Vue;

        // 状态变量
        const applications = ref([]);
        const searchTerm = ref('');
        const isLoading = ref(true);

        // 分页相关
        const currentPage = ref(1);
        const itemsPerPage = ref(10);

        // 过滤后的申请列表（用于搜索）
        const searchFilteredApplications = computed(() => {
            if (!searchTerm.value.trim()) return applications.value;

            const term = searchTerm.value.toLowerCase();
            return applications.value.filter(app => {
                const applicantMatch = (app.applicant || '').toLowerCase().includes(term);
                const contentMatch = (app.content || '').toLowerCase().includes(term);
                return applicantMatch || contentMatch;
            });
        });

        // 分页后的申请列表（最终显示的数据）
        const filteredApplications = computed(() => {
            const filtered = searchFilteredApplications.value;
            const start = (currentPage.value - 1) * itemsPerPage.value;
            const end = start + itemsPerPage.value;
            return filtered.slice(start, end);
        });

        // 总记录数（用于分页组件）
        const totalItems = computed(() => {
            return searchFilteredApplications.value.length;
        });

        // 监听搜索变化，重置页码
        const { watch } = Vue;
        watch(searchTerm, () => {
            currentPage.value = 1;
        });

        // 初始化
        onMounted(() => {
            loadApplications();
        });

        // 加载申请列表
        async function loadApplications() {
            try {
                isLoading.value = true;
                applications.value = await getApplications();
            } catch (error) {
                console.error('加载申请列表失败:', error);
                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    sessionStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('加载申请列表失败: ' + (error.response?.data?.message || error.message));
                }
            } finally {
                isLoading.value = false;
            }
        }

        // 查看详情
        function viewDetail(id) {
            emit('view-detail', id);
        }

        // 分页处理
        function handlePageChange(page) {
            currentPage.value = page;
        }

        // 删除申请
        async function handleDelete(id) {
            if (!confirm('确定要删除这条申请记录吗？')) return;

            // 找到要删除的申请项
            const targetIndex = applications.value.findIndex(app => app.id === id);
            if (targetIndex === -1) {
                alert('申请不存在');
                return;
            }

            // 保存要删除的申请项（用于失败时恢复）
            const deletedApplication = applications.value[targetIndex];

            // 乐观更新：立即从前端列表中移除
            applications.value.splice(targetIndex, 1);

            try {
                const result = await deleteApplication(id);
                if (result.success) {
                    // 显示成功提示（非阻塞）
                    toast.success('申请删除成功');
                } else {
                    // API返回失败，恢复申请到列表中
                    applications.value.splice(targetIndex, 0, deletedApplication);
                    alert('删除失败: ' + result.message);
                }
            } catch (error) {
                // API调用失败，恢复申请到列表中
                applications.value.splice(targetIndex, 0, deletedApplication);

                if (error.response?.status === 401 || error.response?.status === 403) {
                    // 认证失败，跳转到登录页
                    sessionStorage.removeItem('authToken');
                    window.location.href = '/login';
                } else {
                    alert('删除失败: ' + (error.response?.data?.message || error.message));
                }
            }
        }

        // 获取优先级样式
        function getPriorityClass(priority) {
            return PRIORITIES[priority]?.class || 'bg-gray-100';
        }

        return {
            applications,
            searchTerm,
            // 分页相关
            currentPage,
            itemsPerPage,
            totalItems,
            handlePageChange,
            filteredApplications,
            isLoading,
            viewDetail,
            handleDelete,
            getPriorityClass
        };
    },
    template: `
        <section>
            <div class="mb-4">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" v-model="searchTerm" placeholder="搜索申请人或内容..."
                           class="w-full pl-10 p-2 border border-gray-200 rounded-md focus:outline-none focus:border-blue-400 text-gray-700">
                </div>
            </div>

            <div v-if="isLoading" class="py-8 text-center text-gray-500">
                <p>加载中...</p>
            </div>

            <div v-else-if="filteredApplications.length === 0" class="py-8 text-center text-gray-500">
                <p>暂无申请记录</p>
            </div>

            <div v-else class="overflow-x-auto">
                <table class="min-w-full table-auto border-collapse">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="px-6 py-3 text-left">申请人</th>
                            <th class="px-6 py-3 text-left">申请部门</th>
                            <th class="px-6 py-3 text-left">申请日期</th>
                            <th class="px-6 py-3 text-left">紧急程度</th>
                            <th class="px-6 py-3 text-left">附件数量</th>
                            <th class="px-6 py-3 text-left">操作</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <tr v-for="app in filteredApplications" :key="app.id" class="hover:bg-gray-50">
                            <td class="px-6 py-4">{{ app.applicant }}</td>
                            <td class="px-6 py-4">{{ app.department || '' }}</td>
                            <td class="px-6 py-4">{{ app.date }}</td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 rounded-full" :class="getPriorityClass(app.priority)">
                                    {{ app.priority }}
                                </span>
                            </td>
                            <td class="px-6 py-4">{{ app.attachments?.length || 0 }}</td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-1">
                                    <!-- 查看按钮 -->
                                    <button @click="viewDetail(app.id)"
                                            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 transition-all duration-200 group"
                                            title="查看详情">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>

                                    <!-- 删除按钮 -->
                                    <button @click="handleDelete(app.id)"
                                            class="inline-flex items-center justify-center w-8 h-8 rounded-full bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700 transition-all duration-200 group"
                                            title="删除申请">
                                        <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <!-- 分页组件 -->
                <unified-pagination
                    :current-page="currentPage"
                    :total-items="totalItems"
                    :items-per-page="itemsPerPage"
                    @page-change="handlePageChange">
                </unified-pagination>
            </div>
        </section>
    `
};
