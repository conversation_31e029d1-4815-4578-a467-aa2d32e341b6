/**
 * 申请路由
 * 处理申请相关的路由
 */

const express = require('express');
const router = express.Router();
const applicationController = require('../controllers/applicationController');
const { authenticateJWT } = require('../middlewares/auth');
const upload = require('../middlewares/upload');

// 获取所有申请
router.get('/', authenticateJWT, applicationController.getAllApplications);

// 获取待审核申请
router.get('/pending', authenticateJWT, applicationController.getPendingApplications);

// 获取已审核申请
router.get('/approved', authenticateJWT, applicationController.getApprovedApplications);

// 获取申请记录
router.get('/records', authenticateJWT, applicationController.getApplicationRecords);

// 审批通过申请
router.post('/:id/approve', authenticateJWT, applicationController.approveApplication);

// 审批拒绝申请
router.post('/:id/reject', authenticateJWT, applicationController.rejectApplication);

// 获取单个申请详情
router.get('/:id', authenticateJWT, applicationController.getApplicationById);

// 创建新申请
router.post('/',
    authenticateJWT,
    upload.array('attachments', 10),
    applicationController.createApplication
);

// 修改申请
router.put('/:id',
    authenticateJWT,
    upload.array('attachments', 10),
    applicationController.updateApplication
);

// 删除申请
router.delete('/:id', authenticateJWT, applicationController.deleteApplication);

// 下载附件
router.get('/attachments/:id', authenticateJWT, applicationController.downloadAttachment);

module.exports = router;
