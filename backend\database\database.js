/**
 * SQLite数据库连接和初始化
 * 集成连接池和查询优化器
 */

const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const logger = require('../utils/logger');
const MigrationManager = require('./migrationManager');
const { connectionPool } = require('../utils/connectionPool');
const { queryOptimizer } = require('../utils/queryOptimizer');

class DatabaseManager {
    constructor() {
        this.db = null;
        this.dbPath = path.join(__dirname, 'application_system.db');
        this.migrationCompleted = false;
        this.init();
    }

    /**
     * 初始化数据库连接
     */
    init() {
        try {
            // 确保数据库目录存在
            const dbDir = path.dirname(this.dbPath);
            if (!fs.existsSync(dbDir)) {
                fs.mkdirSync(dbDir, { recursive: true });
            }

            // 创建数据库连接
            this.db = new Database(this.dbPath);

            // 启用外键约束
            this.db.pragma('foreign_keys = ON');

            // 设置WAL模式以提高并发性能
            this.db.pragma('journal_mode = WAL');

            // 创建表结构
            this.createTables();

            logger.info(`SQLite数据库初始化成功: ${this.dbPath}`);

            // 异步运行迁移
            this.runMigrationsAsync();
        } catch (error) {
            logger.error('数据库初始化失败:', error);
            throw error;
        }
    }

    /**
     * 异步运行数据库迁移
     */
    async runMigrationsAsync() {
        try {
            const migrationManager = new MigrationManager(this.db);
            await migrationManager.runMigrations();
            this.migrationCompleted = true;
            logger.info('数据库迁移完成');
        } catch (error) {
            logger.error('数据库迁移失败:', error);
            // 迁移失败不应该阻止系统启动，但需要记录错误
        }
    }

    /**
     * 创建数据库表结构
     */
    createTables() {
        // 用户表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS users (
                id TEXT PRIMARY KEY,
                usercode TEXT UNIQUE NOT NULL,
                username TEXT NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL,
                department TEXT,
                email TEXT,
                active INTEGER DEFAULT 1,
                permissions TEXT DEFAULT '[]',
                has_signature INTEGER DEFAULT 0,
                signature_path TEXT,
                last_login_at TEXT,
                last_active_at TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        `);

        // 添加签名字段（如果不存在）
        this.addSignatureFieldsIfNotExists();

        // 申请表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS applications (
                id TEXT PRIMARY KEY,
                application_number TEXT UNIQUE NOT NULL,
                user_id TEXT NOT NULL,
                applicant TEXT NOT NULL,
                department TEXT,
                date TEXT NOT NULL,
                content TEXT NOT NULL,
                amount TEXT,
                priority TEXT DEFAULT 'normal',
                type TEXT DEFAULT 'standard',
                status TEXT DEFAULT 'pending',
                current_stage TEXT,
                need_manager_approval INTEGER DEFAULT 0,
                need_ceo_approval INTEGER DEFAULT 1,
                selected_factory_managers TEXT DEFAULT '[]',
                selected_managers TEXT DEFAULT '[]',
                pdf_path TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        `);

        // 申请附件表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS application_attachments (
                id TEXT PRIMARY KEY,
                application_id TEXT NOT NULL,
                name TEXT NOT NULL,
                path TEXT NOT NULL,
                filename TEXT,
                type TEXT,
                size INTEGER,
                uploaded_at TEXT NOT NULL,
                FOREIGN KEY (application_id) REFERENCES applications (id) ON DELETE CASCADE
            )
        `);

        // 审批历史表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS approval_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                application_id TEXT NOT NULL,
                stage TEXT NOT NULL,
                approver_id TEXT NOT NULL,
                approver_name TEXT NOT NULL,
                approver_role TEXT NOT NULL,
                action TEXT NOT NULL,
                comment TEXT,
                signature_path TEXT,
                timestamp TEXT NOT NULL,
                FOREIGN KEY (application_id) REFERENCES applications (id) ON DELETE CASCADE,
                FOREIGN KEY (approver_id) REFERENCES users (id)
            )
        `);

        // 权限模板表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS permission_templates (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                permissions TEXT NOT NULL DEFAULT '[]',
                is_built_in INTEGER DEFAULT 0,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        `);

        // 部门表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS departments (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                description TEXT,
                active INTEGER DEFAULT 1,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        `);

        // 产品表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS products (
                id TEXT PRIMARY KEY,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                category TEXT,
                specifications TEXT DEFAULT '{}',
                unit TEXT DEFAULT 'pcs',
                standard_time INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 生产工艺流程表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS production_processes (
                id TEXT PRIMARY KEY,
                product_id TEXT NOT NULL,
                process_name TEXT NOT NULL,
                sequence_order INTEGER NOT NULL,
                standard_time INTEGER NOT NULL,
                required_equipment_type TEXT,
                skill_requirements TEXT DEFAULT '[]',
                setup_time INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products(id)
            )
        `);

        // 设备产能表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS equipment_capabilities (
                id TEXT PRIMARY KEY,
                equipment_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                process_id TEXT,
                capacity_per_hour INTEGER NOT NULL,
                efficiency_factor REAL DEFAULT 1.0,
                setup_time INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment(id),
                FOREIGN KEY (product_id) REFERENCES products(id),
                FOREIGN KEY (process_id) REFERENCES production_processes(id)
            )
        `);

        // 操作员技能表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS operator_skills (
                id TEXT PRIMARY KEY,
                operator_id TEXT NOT NULL,
                equipment_id TEXT NOT NULL,
                skill_level INTEGER DEFAULT 1,
                efficiency_factor REAL DEFAULT 1.0,
                certification_date TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (operator_id) REFERENCES users(id),
                FOREIGN KEY (equipment_id) REFERENCES equipment(id)
            )
        `);

        // 设备操作员关联表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS equipment_operators (
                id TEXT PRIMARY KEY,
                equipment_id TEXT NOT NULL,
                operator_id TEXT NOT NULL,
                is_primary INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment(id),
                FOREIGN KEY (operator_id) REFERENCES users(id)
            )
        `);

        // 生产排程表（扩展）
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS schedules (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                product_id TEXT NOT NULL,
                product_name TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                start_time TEXT NOT NULL,
                end_time TEXT NOT NULL,
                status TEXT DEFAULT 'planned',
                priority TEXT DEFAULT 'medium',
                assigned_equipment TEXT DEFAULT '[]',
                assigned_personnel TEXT DEFAULT '[]',
                required_materials TEXT DEFAULT '[]',
                progress INTEGER DEFAULT 0,
                notes TEXT DEFAULT '',
                created_by TEXT NOT NULL,
                order_id TEXT,
                delivery_date TEXT,
                predicted_completion TEXT,
                confidence_level REAL,
                optimization_score REAL,
                alternative_plans TEXT DEFAULT '[]',
                is_intelligent INTEGER DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 排程进度记录表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS schedule_progress (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                schedule_id TEXT NOT NULL,
                progress INTEGER NOT NULL,
                status TEXT NOT NULL,
                notes TEXT DEFAULT '',
                updated_by TEXT NOT NULL,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (schedule_id) REFERENCES schedules(id)
            )
        `);

        // 资源表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS resources (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                type TEXT NOT NULL,
                status TEXT DEFAULT 'available',
                capacity INTEGER DEFAULT 0,
                current_load INTEGER DEFAULT 0,
                department TEXT DEFAULT '',
                specifications TEXT DEFAULT '{}',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 设备表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS equipment (
                id TEXT PRIMARY KEY,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                area TEXT NOT NULL,
                location TEXT NOT NULL,
                responsible TEXT NOT NULL,
                manufacture_date TEXT NOT NULL,
                status TEXT DEFAULT 'active',
                specifications TEXT DEFAULT '{}',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 厂区表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS factories (
                id TEXT PRIMARY KEY,
                name TEXT UNIQUE NOT NULL,
                description TEXT DEFAULT '',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 设备维修记录表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS equipment_maintenance (
                id TEXT PRIMARY KEY,
                equipment_id TEXT NOT NULL,
                type TEXT NOT NULL,
                severity_level TEXT DEFAULT '',
                description TEXT NOT NULL,
                maintenance_date TEXT NOT NULL,
                start_time TEXT DEFAULT '',
                end_time TEXT DEFAULT '',
                cost REAL DEFAULT 0,
                technician TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                notes TEXT DEFAULT '',
                result TEXT DEFAULT '',
                reviewer TEXT DEFAULT '',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        `);

        // 设备健康度记录表 - 基于四维度评估体系
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS equipment_health (
                id TEXT PRIMARY KEY,
                equipment_id TEXT NOT NULL,
                total_score INTEGER NOT NULL,
                health_level TEXT NOT NULL,
                age_score INTEGER NOT NULL,
                repair_frequency_score INTEGER NOT NULL,
                fault_severity_score INTEGER NOT NULL,
                maintenance_score INTEGER NOT NULL,
                assessment_date TEXT NOT NULL,
                assessor TEXT NOT NULL,
                calculation_details TEXT DEFAULT '{}',
                recommendations TEXT DEFAULT '[]',
                next_maintenance_date TEXT,
                failure_probability REAL DEFAULT 0,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        `);

        // 设备健康度历史记录表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS equipment_health_history (
                id TEXT PRIMARY KEY,
                equipment_id TEXT NOT NULL,
                total_score INTEGER NOT NULL,
                health_level TEXT NOT NULL,
                age_score INTEGER NOT NULL,
                repair_frequency_score INTEGER NOT NULL,
                fault_severity_score INTEGER NOT NULL,
                maintenance_score INTEGER NOT NULL,
                assessment_date TEXT NOT NULL,
                assessor TEXT NOT NULL,
                calculation_details TEXT DEFAULT '{}',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (equipment_id) REFERENCES equipment (id)
            )
        `);

        // 质量检测报告表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS quality_reports (
                id TEXT PRIMARY KEY,
                report_number TEXT UNIQUE NOT NULL,
                title TEXT NOT NULL,
                description TEXT DEFAULT '',
                test_type TEXT NOT NULL,
                test_date TEXT NOT NULL,
                sample_info TEXT DEFAULT '',
                test_method TEXT DEFAULT '',
                test_standard TEXT DEFAULT '',
                test_result TEXT DEFAULT '',
                conclusion TEXT DEFAULT '',
                uploaded_by TEXT NOT NULL,
                uploaded_at TEXT NOT NULL,
                status TEXT DEFAULT 'published',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (uploaded_by) REFERENCES users (id)
            )
        `);

        // 质量检测报告文件表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS quality_report_files (
                id TEXT PRIMARY KEY,
                report_id TEXT NOT NULL,
                original_filename TEXT NOT NULL,
                stored_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                file_type TEXT NOT NULL,
                mime_type TEXT NOT NULL,
                uploaded_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (report_id) REFERENCES quality_reports (id) ON DELETE CASCADE
            )
        `);

        // 质量检测报告编号预留表（防止并发冲突）
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS quality_report_number_reservations (
                report_number TEXT PRIMARY KEY,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 文件管理 - 客户信息表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS file_management_customers (
                id TEXT PRIMARY KEY,
                customer_name TEXT UNIQUE NOT NULL,
                customer_code TEXT,
                contact_person TEXT,
                contact_email TEXT,
                contact_phone TEXT,
                address TEXT,
                description TEXT DEFAULT '',
                active INTEGER DEFAULT 1,
                created_by TEXT NOT NULL,
                updated_by TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (updated_by) REFERENCES users (id)
            )
        `);

        // 文件管理 - 产品型号表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS file_management_products (
                id TEXT PRIMARY KEY,
                customer_id TEXT NOT NULL,
                product_model TEXT NOT NULL,
                product_name TEXT,
                batch_number TEXT,
                specification TEXT DEFAULT '',
                description TEXT DEFAULT '',
                active INTEGER DEFAULT 1,
                created_by TEXT NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES file_management_customers (id) ON DELETE CASCADE,
                FOREIGN KEY (created_by) REFERENCES users (id),
                UNIQUE(customer_id, product_model, batch_number)
            )
        `);

        // 文件管理 - 文件记录表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS file_management_files (
                id TEXT PRIMARY KEY,
                file_number TEXT UNIQUE NOT NULL,
                customer_id TEXT NOT NULL,
                product_id TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT DEFAULT '',
                version INTEGER DEFAULT 1,
                is_first_version INTEGER DEFAULT 1,
                change_description TEXT DEFAULT '',
                status TEXT DEFAULT 'active',
                uploaded_by TEXT NOT NULL,
                uploaded_at TEXT NOT NULL,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES file_management_customers (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES file_management_products (id) ON DELETE CASCADE,
                FOREIGN KEY (uploaded_by) REFERENCES users (id)
            )
        `);

        // 文件管理 - 附件文件表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS file_management_attachments (
                id TEXT PRIMARY KEY,
                file_record_id TEXT NOT NULL,
                original_filename TEXT NOT NULL,
                stored_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER NOT NULL,
                file_type TEXT NOT NULL,
                mime_type TEXT NOT NULL,
                uploaded_at TEXT DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (file_record_id) REFERENCES file_management_files (id) ON DELETE CASCADE
            )
        `);

        // 文件管理 - 通知记录表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS file_management_notifications (
                id TEXT PRIMARY KEY,
                file_record_id TEXT NOT NULL,
                notified_user_id TEXT NOT NULL,
                notification_type TEXT DEFAULT 'new_file',
                sent_at TEXT DEFAULT CURRENT_TIMESTAMP,
                confirmed_at TEXT,
                confirmed INTEGER DEFAULT 0,
                FOREIGN KEY (file_record_id) REFERENCES file_management_files (id) ON DELETE CASCADE,
                FOREIGN KEY (notified_user_id) REFERENCES users (id),
                UNIQUE(file_record_id, notified_user_id)
            )
        `);

        // 文件管理 - 文件编号预留表（防止并发冲突）
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS file_management_number_reservations (
                file_number TEXT PRIMARY KEY,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 质量检测报告编号记录表（记录所有已使用的编号，包括已删除的）
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS quality_report_numbers (
                report_number TEXT PRIMARY KEY,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 创建索引以提高查询性能
        this.createIndexes();

        // 执行数据库迁移
        this.runMigrations();
    }

    /**
     * 执行数据库迁移
     */
    runMigrations() {
        try {
            // 检查 selected_managers 字段是否存在
            const tableInfo = this.db.pragma('table_info(applications)');
            const hasSelectedManagersField = tableInfo.some(column => column.name === 'selected_managers');

            if (!hasSelectedManagersField) {
                logger.info('添加 selected_managers 字段到 applications 表');
                this.db.exec(`
                    ALTER TABLE applications
                    ADD COLUMN selected_managers TEXT DEFAULT '[]'
                `);

                // 为现有记录设置默认值
                const updateResult = this.db.prepare(`
                    UPDATE applications
                    SET selected_managers = '[]'
                    WHERE selected_managers IS NULL
                `).run();

                logger.info(`selected_managers 字段添加成功，更新了 ${updateResult.changes} 条记录`);
            }

            // 迁移质量检测报告编号
            const migrateQualityReportNumbers = require('./migrations/migrate_quality_report_numbers');
            migrateQualityReportNumbers(this.db);

            // 迁移维修保养记录表结构
            this.migrateMaintenanceTable();

            // 迁移文件管理客户表结构
            this.migrateFileManagementCustomersTable();

            // 检查 need_ceo_approval 字段是否存在
            const tableInfo2 = this.db.pragma('table_info(applications)');
            const hasNeedCeoApprovalField = tableInfo2.some(column => column.name === 'need_ceo_approval');

            if (!hasNeedCeoApprovalField) {
                logger.info('添加 need_ceo_approval 字段到 applications 表');
                this.db.exec(`
                    ALTER TABLE applications
                    ADD COLUMN need_ceo_approval INTEGER DEFAULT 1
                `);

                // 为现有记录设置默认值
                const updateResult2 = this.db.prepare(`
                    UPDATE applications
                    SET need_ceo_approval = 1
                    WHERE need_ceo_approval IS NULL
                `).run();

                logger.info(`need_ceo_approval 字段添加成功，更新了 ${updateResult2.changes} 条记录`);
            }
        } catch (error) {
            logger.error('数据库迁移失败:', error);
        }
    }

    /**
     * 创建数据库索引
     */
    createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_users_usercode ON users (usercode)',
            'CREATE INDEX IF NOT EXISTS idx_users_role ON users (role)',
            'CREATE INDEX IF NOT EXISTS idx_users_active ON users (active)',
            'CREATE INDEX IF NOT EXISTS idx_applications_user_id ON applications (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_applications_status ON applications (status)',
            'CREATE INDEX IF NOT EXISTS idx_applications_current_stage ON applications (current_stage)',
            'CREATE INDEX IF NOT EXISTS idx_applications_created_at ON applications (created_at)',
            'CREATE INDEX IF NOT EXISTS idx_approval_history_application_id ON approval_history (application_id)',
            'CREATE INDEX IF NOT EXISTS idx_approval_history_approver_id ON approval_history (approver_id)',
            'CREATE INDEX IF NOT EXISTS idx_application_attachments_application_id ON application_attachments (application_id)',
            'CREATE INDEX IF NOT EXISTS idx_schedules_status ON schedules (status)',
            'CREATE INDEX IF NOT EXISTS idx_schedules_priority ON schedules (priority)',
            'CREATE INDEX IF NOT EXISTS idx_schedules_created_by ON schedules (created_by)',
            'CREATE INDEX IF NOT EXISTS idx_schedules_start_time ON schedules (start_time)',
            'CREATE INDEX IF NOT EXISTS idx_schedules_end_time ON schedules (end_time)',
            'CREATE INDEX IF NOT EXISTS idx_schedule_progress_schedule_id ON schedule_progress (schedule_id)',
            'CREATE INDEX IF NOT EXISTS idx_resources_type ON resources (type)',
            'CREATE INDEX IF NOT EXISTS idx_resources_status ON resources (status)',
            // 设备相关索引
            'CREATE INDEX IF NOT EXISTS idx_equipment_code ON equipment (code)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_area ON equipment (area)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_status ON equipment (status)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_responsible ON equipment (responsible)',
            'CREATE INDEX IF NOT EXISTS idx_factories_name ON factories (name)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_equipment_id ON equipment_maintenance (equipment_id)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_status ON equipment_maintenance (status)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_maintenance_start_date ON equipment_maintenance (start_date)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_equipment_id ON equipment_health (equipment_id)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_assessment_date ON equipment_health (assessment_date)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_total_score ON equipment_health (total_score)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_level ON equipment_health (health_level)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_history_equipment_id ON equipment_health_history (equipment_id)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_history_assessment_date ON equipment_health_history (assessment_date)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_total_score ON equipment_health (total_score)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_level ON equipment_health (health_level)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_history_equipment_id ON equipment_health_history (equipment_id)',
            'CREATE INDEX IF NOT EXISTS idx_equipment_health_history_assessment_date ON equipment_health_history (assessment_date)',
            // 质量管理相关索引
            'CREATE INDEX IF NOT EXISTS idx_quality_reports_report_number ON quality_reports (report_number)',
            'CREATE INDEX IF NOT EXISTS idx_quality_reports_uploaded_by ON quality_reports (uploaded_by)',
            'CREATE INDEX IF NOT EXISTS idx_quality_reports_test_type ON quality_reports (test_type)',
            'CREATE INDEX IF NOT EXISTS idx_quality_reports_test_date ON quality_reports (test_date)',
            'CREATE INDEX IF NOT EXISTS idx_quality_reports_status ON quality_reports (status)',
            'CREATE INDEX IF NOT EXISTS idx_quality_reports_created_at ON quality_reports (created_at)',
            'CREATE INDEX IF NOT EXISTS idx_quality_report_files_report_id ON quality_report_files (report_id)',
            'CREATE INDEX IF NOT EXISTS idx_quality_report_files_file_type ON quality_report_files (file_type)'
        ];

        indexes.forEach(indexSql => {
            try {
                this.db.exec(indexSql);
            } catch (error) {
                logger.warn(`创建索引失败: ${error.message}`);
            }
        });
    }

    /**
     * 迁移文件管理客户表结构
     */
    migrateFileManagementCustomersTable() {
        try {
            // 检查 updated_by 字段是否存在
            const tableInfo = this.db.pragma('table_info(file_management_customers)');
            const hasUpdatedByField = tableInfo.some(column => column.name === 'updated_by');

            if (!hasUpdatedByField) {
                console.log('开始迁移文件管理客户表结构...');

                // 添加 updated_by 字段
                this.db.exec(`
                    ALTER TABLE file_management_customers
                    ADD COLUMN updated_by TEXT REFERENCES users(id)
                `);

                console.log('文件管理客户表结构迁移完成');
            } else {
                console.log('文件管理客户表已有新字段，跳过迁移');
            }
        } catch (error) {
            console.error('迁移文件管理客户表结构失败:', error);
        }
    }

    /**
     * 迁移文件管理客户表结构
     */
    migrateFileManagementCustomersTable() {
        try {
            // 检查表是否存在
            const tableExists = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='file_management_customers'").get();

            if (tableExists) {
                // 检查 updated_by 字段是否存在
                const tableInfo = this.db.pragma('table_info(file_management_customers)');
                const hasUpdatedByField = tableInfo.some(column => column.name === 'updated_by');

                if (!hasUpdatedByField) {
                    console.log('开始迁移文件管理客户表结构...');

                    // 添加 updated_by 字段
                    this.db.exec(`
                        ALTER TABLE file_management_customers
                        ADD COLUMN updated_by TEXT REFERENCES users(id)
                    `);

                    console.log('文件管理客户表结构迁移完成');
                } else {
                    console.log('文件管理客户表已有新字段，跳过迁移');
                }
            }
        } catch (error) {
            console.error('迁移文件管理客户表结构失败:', error);
        }
    }

    /**
     * 迁移维修保养记录表结构
     */
    migrateMaintenanceTable() {
        try {
            // 检查表是否存在新字段
            const tableInfo = this.db.pragma('table_info(equipment_maintenance)');
            const hasNewFields = tableInfo.some(column =>
                ['severity_level', 'maintenance_date', 'start_time', 'end_time', 'result', 'reviewer'].includes(column.name)
            );

            if (!hasNewFields) {
                console.log('开始迁移维修保养记录表结构...');

                // 备份现有数据
                const existingData = this.db.prepare('SELECT * FROM equipment_maintenance').all();

                // 删除旧表
                this.db.exec('DROP TABLE IF EXISTS equipment_maintenance_backup');
                this.db.exec('ALTER TABLE equipment_maintenance RENAME TO equipment_maintenance_backup');

                // 创建新表
                this.db.exec(`
                    CREATE TABLE equipment_maintenance (
                        id TEXT PRIMARY KEY,
                        equipment_id TEXT NOT NULL,
                        type TEXT NOT NULL,
                        severity_level TEXT DEFAULT '',
                        description TEXT NOT NULL,
                        maintenance_date TEXT NOT NULL,
                        start_time TEXT DEFAULT '',
                        end_time TEXT DEFAULT '',
                        cost REAL DEFAULT 0,
                        technician TEXT NOT NULL,
                        status TEXT DEFAULT 'pending',
                        notes TEXT DEFAULT '',
                        result TEXT DEFAULT '',
                        reviewer TEXT DEFAULT '',
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (equipment_id) REFERENCES equipment (id)
                    )
                `);

                // 迁移数据
                if (existingData.length > 0) {
                    const insertStmt = this.db.prepare(`
                        INSERT INTO equipment_maintenance (
                            id, equipment_id, type, severity_level, description,
                            maintenance_date, start_time, end_time, cost, technician,
                            status, notes, result, reviewer, created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    `);

                    for (const record of existingData) {
                        insertStmt.run(
                            record.id,
                            record.equipment_id,
                            record.type,
                            '', // severity_level
                            record.description,
                            record.start_date || record.maintenance_date || new Date().toISOString().split('T')[0], // maintenance_date
                            '', // start_time
                            '', // end_time
                            record.cost || 0,
                            record.technician,
                            record.status || 'pending',
                            record.notes || '',
                            '', // result
                            '', // reviewer
                            record.created_at,
                            record.updated_at
                        );
                    }
                }

                // 删除备份表
                this.db.exec('DROP TABLE IF EXISTS equipment_maintenance_backup');

                console.log('维修保养记录表结构迁移完成');
            } else {
                console.log('维修保养记录表已有新字段，跳过迁移');
            }
        } catch (error) {
            console.error('迁移维修保养记录表结构失败:', error);
        }
    }

    /**
     * 获取数据库连接
     */
    getConnection() {
        if (!this.db) {
            throw new Error('数据库未初始化');
        }
        return this.db;
    }

    /**
     * 获取连接池
     */
    getConnectionPool() {
        return connectionPool;
    }

    /**
     * 执行查询（使用连接池）
     */
    async executeQuery(queryFn) {
        return await connectionPool.execute(queryFn);
    }

    /**
     * 执行事务（使用连接池）
     */
    async executeTransaction(transactionFn) {
        return await connectionPool.transaction(transactionFn);
    }

    /**
     * 开始事务（传统方式）
     */
    beginTransaction() {
        return this.db.transaction();
    }

    /**
     * 创建优化的预编译语句
     */
    prepareOptimized(sql) {
        const statement = this.db.prepare(sql);
        return queryOptimizer.wrapQuery(statement, sql);
    }

    /**
     * 创建批量插入器
     */
    createBatchInsert(tableName, columns) {
        return queryOptimizer.createBatchInsert(this.db, tableName, columns);
    }

    /**
     * 创建批量更新器
     */
    createBatchUpdate(tableName, setColumns, whereColumns) {
        return queryOptimizer.createBatchUpdate(this.db, tableName, setColumns, whereColumns);
    }

    /**
     * 关闭数据库连接
     */
    close() {
        if (this.db) {
            this.db.close();
            this.db = null;
            logger.info('数据库连接已关闭');
        }
    }

    /**
     * 执行数据库备份
     */
    backup(backupPath) {
        try {
            if (!this.db) {
                throw new Error('数据库未初始化');
            }

            this.db.backup(backupPath);
            logger.info(`数据库备份成功: ${backupPath}`);
            return true;
        } catch (error) {
            logger.error('数据库备份失败:', error);
            return false;
        }
    }

    /**
     * 获取数据库性能统计
     */
    getPerformanceStats() {
        const connectionStats = connectionPool.getStats();
        const queryStats = queryOptimizer.getQueryStats();

        return {
            connection: connectionStats,
            query: queryStats,
            database: {
                path: this.dbPath,
                size: this.getDatabaseSize(),
                lastBackup: this.getLastBackupTime()
            }
        };
    }

    /**
     * 获取查询优化建议
     */
    getOptimizationSuggestions() {
        return queryOptimizer.generateOptimizationSuggestions();
    }

    /**
     * 获取慢查询列表
     */
    getSlowQueries(limit = 10) {
        return queryOptimizer.getSlowQueries(limit);
    }

    /**
     * 获取数据库文件大小
     */
    getDatabaseSize() {
        try {
            const stats = fs.statSync(this.dbPath);
            return {
                bytes: stats.size,
                mb: (stats.size / 1024 / 1024).toFixed(2)
            };
        } catch (error) {
            return { bytes: 0, mb: '0.00' };
        }
    }

    /**
     * 获取最后备份时间
     */
    getLastBackupTime() {
        // 这里可以实现备份时间跟踪逻辑
        return null;
    }

    /**
     * 数据库健康检查
     */
    async healthCheck() {
        try {
            // 执行简单查询测试连接
            const result = this.db.prepare('SELECT 1 as test').get();

            // 检查WAL文件大小
            const walPath = this.dbPath + '-wal';
            let walSize = 0;
            if (fs.existsSync(walPath)) {
                walSize = fs.statSync(walPath).size;
            }

            return {
                status: 'healthy',
                connection: result.test === 1,
                walSize: {
                    bytes: walSize,
                    mb: (walSize / 1024 / 1024).toFixed(2)
                },
                performance: this.getPerformanceStats()
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error.message,
                connection: false
            };
        }
    }

    /**
     * 优化数据库
     */
    optimize() {
        try {
            logger.info('开始数据库优化...');

            // 执行VACUUM以压缩数据库
            this.db.exec('VACUUM');

            // 分析表以更新统计信息
            this.db.exec('ANALYZE');

            // 重建索引
            this.db.exec('REINDEX');

            logger.info('数据库优化完成');
            return true;
        } catch (error) {
            logger.error('数据库优化失败:', error);
            return false;
        }
    }

    /**
     * 添加签名字段（如果不存在）
     */
    addSignatureFieldsIfNotExists() {
        try {
            // 检查是否已经有签名字段
            const tableInfo = this.db.prepare("PRAGMA table_info(users)").all();
            const hasSignatureField = tableInfo.some(column => column.name === 'has_signature');
            const signaturePathField = tableInfo.some(column => column.name === 'signature_path');

            if (!hasSignatureField) {
                this.db.exec('ALTER TABLE users ADD COLUMN has_signature INTEGER DEFAULT 0');
                logger.info('添加 has_signature 字段到 users 表');
            }

            if (!signaturePathField) {
                this.db.exec('ALTER TABLE users ADD COLUMN signature_path TEXT');
                logger.info('添加 signature_path 字段到 users 表');
            }
        } catch (error) {
            logger.error('添加签名字段失败:', error);
        }
    }
}

// 创建单例实例
const databaseManager = new DatabaseManager();

module.exports = databaseManager;
